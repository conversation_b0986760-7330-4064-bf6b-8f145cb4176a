package constants;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class Constants {
    public static final String PROJECT_PATH = System.getProperty("user.dir");
    public static final String SCREEN_RECORDING_PATH = PROJECT_PATH + File.separator + "recordings";
    public static final String TEXT_DOES_NOT_MATCH = "Text does not match";

    // Log messages for context switching
    public static final String AVAILABLE_CONTEXTS_MESSAGE = "Available contexts: ";
    public static final String SWITCHED_TO_CONTEXT_MESSAGE = "Switched to context: ";
    public static final String AUTO_DETECTED_WEBVIEW_MESSAGE = "Auto-detected and switched to WebView: ";

    // Log messages for iOS WebView operations
    public static final String IOS_WAITING_SECOND_WEBVIEW_MESSAGE = "iOS: Waiting for second WebView to appear...";
    public static final String IOS_FOUND_WEBVIEWS_MESSAGE = "iOS: Found WebViews: ";
    public static final String IOS_SELECTED_SECOND_WEBVIEW_MESSAGE = "iOS: Selected second WebView: ";
    public static final String IOS_SECOND_WEBVIEW_TIMEOUT_MESSAGE = "iOS: Second WebView timeout, checking for single WebView...";
    public static final String IOS_USING_SINGLE_WEBVIEW_MESSAGE = "iOS: Using single WebView as fallback: ";
    public static final String IOS_FALLBACK_FAILED_MESSAGE = "iOS: Fallback WebView search failed: ";
    public static final String IOS_NO_WEBVIEW_FOUND_MESSAGE = "iOS: No WebView found after timeout";

    // Log messages for WebView readiness
    public static final String WEBVIEW_WAITING_READY_MESSAGE = "Waiting for WebView to be ready...";
    public static final String WEBVIEW_IS_READY_MESSAGE = "WebView is ready. ";
    public static final String WEBVIEW_READINESS_TIMEOUT_MESSAGE = "WebView readiness timeout after %d seconds.";

    // Context-related constants
    public static final String CONTEXT_TYPE_WEBVIEW = "WEBVIEW";
    public static final String CONTEXT_TYPE_CHROME_WEBVIEW = "WEBVIEW_chrome";
    public static final String CONTEXT_INTERRUPTED_MESSAGE = "Interrupted while waiting for context: ";
    public static final String CONTEXT_NOT_FOUND_MESSAGE = "Context '%s' not found. Available contexts: %s";

    // --- Added for context switching refactor ---
    public static final String WEBVIEW_PREFIX = "WEBVIEW_";

    public static String getVideoPath() {
        File screenRecordingsDir = new File(SCREEN_RECORDING_PATH);
        if (!screenRecordingsDir.exists()) {
            screenRecordingsDir.mkdir();
        }
        return SCREEN_RECORDING_PATH;
    }

    public static String getCurrentDateTime() {
        return "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd-MM-yyyy-HH:mm"));
    }
}
