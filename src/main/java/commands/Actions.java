package commands;

import constants.Constants;
import driver.DriverManager;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.ios.IOSDriver;
import io.appium.java_client.pagefactory.AppiumFieldDecorator;

import org.openqa.selenium.interactions.PointerInput;
import org.openqa.selenium.interactions.Sequence;
import logger.ConsoleLogger;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.FluentWait;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.Collections;

import static constants.Localization.getLocalizationText;

/**
 * Provides a set of reusable Appium-based actions for both Android and iOS platforms.
 * This class simplifies interactions such as clicking, typing, swiping and context switching
 * to ensure consistency and reduce code duplication in test scripts.
 * <p>
 * Designed for use in automated UI testing across mobile platforms.
 *
 * <AUTHOR>
 * @since 1.0
 */
@SuppressWarnings("unused")
public class Actions {
    protected static final Logger LOGGER = ConsoleLogger.getLogger();
    private final AppiumDriver driver;

    public Actions() {
        this.driver = DriverManager.getDriver();
        PageFactory.initElements(new AppiumFieldDecorator(driver), this);
    }

    /**
     * Returns the current Appium driver instance.
     *
     * @return The AppiumDriver instance.
     */
    public AppiumDriver getDriver() {
        return driver;
    }

    /**
     * Navigates to the specified URL in the mobile browser context.
     *
     * @param url The URL to open.
     */
    public void navigate(String url) {
        driver.get(url);
    }

    /**
     * Clicks on the provided web element after waiting for it to be visible and clickable.
     *
     * @param element The target WebElement to click.
     */
    public void click(WebElement element) {
        LOGGER.info("Click on element: " + element.getText());
        waitForElementToBeVisible(element);
        waitForElementToBeClickable(element);
        element.click();
    }

    /**
     * Performs a JavaScript-based click on the specified web element.
     * Useful for cases where regular Selenium clicks may not work.
     *
     * @param element The element to be clicked using JavaScript.
     */
    public void clickWithJavaScriptExecutor(WebElement element) {
        waitForElementToBeVisible(element);
        waitForElementToBeClickable(element);
        JavascriptExecutor js = getDriver();
        js.executeScript("arguments[0].click();", element);
    }

    /**
     * Verifies text content of a UI element by comparing it to localized expectations for Android or iOS.
     *
     * @param expectedAndroid Expected text on Android.
     * @param expectedIOS     Expected text on iOS.
     * @param element         The element whose text should be verified.
     */
    public void verifyPlatformSpecificText(String expectedAndroid, String expectedIOS, WebElement element) {
        String expectedText = "";
        String actualText = getText(element);
        if (isAndroid()) {
            expectedText = getLocalizationText(expectedAndroid);
        } else if (isIos()) {
            expectedText = getLocalizationText(expectedIOS);
        }
        Assertions.assertEquals(expectedText, actualText, Constants.TEXT_DOES_NOT_MATCH);
    }

    /**
     * Sends the provided text to the input element.
     *
     * @param element The input WebElement.
     * @param text    The string to type.
     */
    public void type(WebElement element, String text) {
        LOGGER.info("Typing into element..");
        element.sendKeys(text);
    }

    /**
     * Clears the contents of the input element.
     *
     * @param element The element to clear.
     */
    public void clear(WebElement element) {
        element.clear();
    }

    /**
     * Returns the visible text of the element.
     *
     * @param element The element to retrieve text from.
     * @return The element's text.
     */
    public String getText(WebElement element) {
        return element.getText();
    }

    /**
     * Checks if the element is displayed.
     *
     * @param element The element to check.
     * @return True if displayed, otherwise false.
     */
    public boolean isDisplayed(WebElement element) {
        return element.isDisplayed();
    }

    /**
     * Checks if the element is not displayed or not present.
     *
     * @param element The element to check.
     * @return True if not displayed or not found, otherwise false.
     */
    public boolean isNotDisplayed(WebElement element) {
        try {
            return !element.isDisplayed();
        } catch (NoSuchElementException e) {
            return true;
        }
    }

    /**
     * Checks if the element is enabled.
     *
     * @param element The element to check.
     * @return True if enabled, otherwise false.
     */
    public boolean isEnabled(WebElement element) {
        return element.isEnabled();
    }

    /**
     * Checks if the element is disabled.
     *
     * @param element The element to check.
     * @return True if disabled, otherwise false.
     */
    public boolean isDisabled(WebElement element) {
        return !element.isEnabled();
    }

    /**
     * Determines if the current driver is an AndroidDriver.
     *
     * @return True if AndroidDriver, otherwise false.
     */
    public boolean isAndroid() {
        return driver instanceof AndroidDriver;
    }

    /**
     * Determines if the current driver is an IOSDriver.
     *
     * @return True if IOSDriver, otherwise false.
     */
    public boolean isIos() {
        return driver instanceof IOSDriver;
    }

    /**
     * Performs a swipe gesture from top to bottom of the screen.
     */
    public void swipeTopToBottom() {
        int screenWidth = driver.manage().window().getSize().getWidth();
        int screenHeight = driver.manage().window().getSize().getHeight();

        int startX = screenWidth / 2;
        int startY = (int) (screenHeight * 0.2);
        int endY = (int) (screenHeight * 0.8);

        swipe(startX, startY, startX, endY);
    }

    /**
     * Performs a swipe gesture from right to left on the given element.
     *
     * @param element The element on which to perform the swipe.
     */
    public void swipeRightToLeft(WebElement element) {
        int startX = element.getLocation().getX() + element.getSize().getWidth();
        int endX = element.getLocation().getX();
        int centerY = element.getLocation().getY() + (element.getSize().getHeight() / 2);

        swipe(startX, centerY, endX, centerY);
    }

    /**
     * Performs a swipe gesture from the center of the element to its right edge.
     *
     * @param element The element on which to perform the swipe.
     */
    public void swipeFromElementCenterToRight(WebElement element) {
        int startX = element.getLocation().getX() + (element.getSize().getWidth() / 2);
        int endX = element.getLocation().getX() + element.getSize().getWidth() - 5; // Slightly before the edge
        int centerY = element.getLocation().getY() + (element.getSize().getHeight() / 2);

        swipe(startX, centerY, endX, centerY);
    }

    /**
     * Verifies that the element's text matches the localized expected text.
     *
     * @param expectedText The key to the localized expected text.
     * @param element      The element whose text should be verified.
     */
    public void verifyLocalizedElementText(String expectedText, WebElement element) {
        String expectedLocalizedText = getLocalizationText(expectedText);
        String actualText = getText(element);
        Assertions.assertEquals(expectedLocalizedText, actualText, Constants.TEXT_DOES_NOT_MATCH);
    }

    /**
     * Verifies that the element's text matches the expected text exactly.
     *
     * @param expectedText The expected text.
     * @param element      The element whose text should be verified.
     */
    public void verifyElementText(String expectedText, WebElement element) {
        String actualText = getText(element);
        Assertions.assertEquals(expectedText, actualText, Constants.TEXT_DOES_NOT_MATCH);
    }

    /**
     * Hides the keyboard for both Android and iOS platforms.
     * Uses platform-specific methods for keyboard dismissal.
     */
    public void hideKeyboard() {
        try {
            if (isAndroid()) {
                AndroidDriver androidDriver = (AndroidDriver) driver;
                ((AndroidDriver) driver).hideKeyboard();
                LOGGER.info("Android keyboard hidden");
            } else if (isIos()) {
                try {
                    ((IOSDriver) driver).hideKeyboard("Done");
                    LOGGER.info("iOS keyboard hidden using Done button");
                } catch (Exception e1) {
                try {
                    int screenHeight = driver.manage().window().getSize().getHeight();
                    int screenWidth = driver.manage().window().getSize().getWidth();

                    PointerInput finger = new PointerInput(PointerInput.Kind.TOUCH, "finger");
                    Sequence tap = new Sequence(finger, 1);
                    tap.addAction(finger.createPointerMove(Duration.ZERO, PointerInput.Origin.viewport(),
                            screenWidth / 94, screenHeight / 46));
                    tap.addAction(finger.createPointerDown(PointerInput.MouseButton.LEFT.asArg()));
                    tap.addAction(finger.createPointerUp(PointerInput.MouseButton.LEFT.asArg()));

                    driver.perform(Collections.singletonList(tap));
                    LOGGER.info("iOS keyboard hidden by tapping outside");
                } catch (Exception e3) {
                    LOGGER.warning("Could not hide iOS keyboard with any strategy: " + e3.getMessage());
                }
                try {
                    ((IOSDriver) driver).hideKeyboard("go");
                    LOGGER.info("iOS keyboard hidden using go button");
                } catch (Exception e5) {


                        try {
                            ((IOSDriver) driver).hideKeyboard("Return");
                            LOGGER.info("iOS keyboard hidden using Return key");
                        } catch (Exception e2) {

                        }
                    }
                }
            } else {
                LOGGER.warning("Unsupported driver type for hiding keyboard.");
            }
        } catch (Exception e) {
            LOGGER.warning("Error while hiding keyboard: " + e.getMessage());
        }
    }

    /**
     * Switches to the specified context view.
     * For "WEBVIEW", automatically detects dynamic WebView contexts (e.g., WEBVIEW_7924.113).
     * For other contexts, uses exact matching.
     * Waits up to 5 seconds, checking every second for the desired context to become available.
     *
     * @param context The context to switch to. Use "WEBVIEW" for auto-detection of any WebView context.
     */
    public void switchToView(String context) {
        for (int i = 0; i < 5; i++) {
            Set<String> contextHandles = getCurrentContexts();
            LOGGER.info(Constants.AVAILABLE_CONTEXTS_MESSAGE + contextHandles);

            if (Constants.CONTEXT_TYPE_WEBVIEW.equalsIgnoreCase(context)) {
                String webViewContext = getWebViewContext(contextHandles);
                if (webViewContext != null) {
                    switchToContext(webViewContext);
                    LOGGER.info(Constants.AUTO_DETECTED_WEBVIEW_MESSAGE + webViewContext);
                    waitForWebViewIfNeeded(context);
                    driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(Constants.IMPLICIT_WAIT_SECONDS));
                    return;
                }
            } else {
                String matchedContext = contextHandles.stream()
                        .filter(ctx -> ctx.contains(context))
                        .findFirst()
                        .orElse(null);
                if (matchedContext != null) {
                    switchToContext(matchedContext);
                    LOGGER.info(Constants.SWITCHED_TO_CONTEXT_MESSAGE + matchedContext);
                    driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(Constants.IMPLICIT_WAIT_SECONDS));
                    return;
                }
            }

            try {
                Thread.sleep(Constants.CONTEXT_SWITCH_RETRY_INTERVAL_MS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException(Constants.CONTEXT_INTERRUPTED_MESSAGE + context);
            }
        }

        Set<String> finalContexts = getCurrentContexts();
        LOGGER.warning(String.format(Constants.CONTEXT_NOT_FOUND_MESSAGE, context, finalContexts));
        driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(Constants.IMPLICIT_WAIT_SECONDS));
    }

    private String getWebViewContext(Set<String> contextHandles) {
        if (isIos()) {
            return waitForSecondWebView(contextHandles);
        }
        if (isAndroid()) {
            if (contextHandles.contains(Constants.CONTEXT_TYPE_CHROME_WEBVIEW)) {
                return Constants.CONTEXT_TYPE_CHROME_WEBVIEW;
            }
            return contextHandles.stream()
                    .filter(ctx -> ctx.startsWith(Constants.WEBVIEW_PREFIX))
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }

    private void waitForWebViewIfNeeded(String context) {
        if (Constants.CONTEXT_TYPE_WEBVIEW.equalsIgnoreCase(context)) {
            waitForWebViewToBeReady();
        }
    }

    /**
     * Helper method to get current context handles for any driver type.
     *
     * @return Set of available context handles
     */
    private Set<String> getCurrentContexts() {
        if (isAndroid()) {
            return ((AndroidDriver) driver).getContextHandles();
        } else if (isIos()) {
            return ((IOSDriver) driver).getContextHandles();
        }
        return Collections.emptySet();
    }

    /**
     * Helper method to switch to a specific context for any driver type.
     *
     * @param contextName The context name to switch to
     */
    private void switchToContext(String contextName) {
        if (isAndroid()) {
            ((AndroidDriver) driver).context(contextName);
        } else if (isIos()) {
            ((IOSDriver) driver).context(contextName);
        }
    }

    /**
     * Čeka da se pojavi WebView na iOS-u i vraća WebView sa najvećim brojem iza tačke.
     * Pojednostavljena verzija koja direktno bira WebView sa najvećim brojem.
     *
     * @param initialContexts Početni set konteksta
     * @return Izabrani WebView kontekst, ili null ako nije pronađen
     */
    private String waitForSecondWebView(Set<String> initialContexts) {
        LOGGER.info("iOS: Waiting for WebView (selecting last from original order)...");

        try {
            // Direktno pokušaj da nađeš WebView kontekste
            Set<String> contexts = ((IOSDriver) driver).getContextHandles();
            LOGGER.info("iOS: Available contexts: " + contexts);

            // Filtriraj samo WebView kontekste i zadrži originalni redosled
            List<String> webViews = new ArrayList<>();
            for (String ctx : contexts) {
                if (ctx.startsWith("WEBVIEW_") && !ctx.equals("NATIVE_APP")) {
                    webViews.add(ctx);
                }
            }

            if (webViews.isEmpty()) {
                LOGGER.warning("iOS: No WebViews found");
                return null;
            }

            LOGGER.info("iOS: Found WebViews in original order: " + webViews);

            // Uzmi poslednji WebView iz originalnog redosleda (kako ih Appium vraća)
            String selectedWebView = webViews.get(webViews.size() - 1);
            LOGGER.info("iOS: Selected last WebView from original order: " + selectedWebView);

            return selectedWebView;

        } catch (Exception e) {
            LOGGER.warning("iOS: Error finding WebView: " + e.getMessage());
            return null;
        }
    }

    /**
     * Pomoćna metoda koja izdvaja broj verzije iz imena WebView-a.
     * Na primer, iz "WEBVIEW_2811.31" vraća 31.0
     *
     * @param webViewName Ime WebView-a
     * @return Broj verzije kao double, ili -1 ako ne može da se parsira
     */
    private double getVersionNumber(String webViewName) {
        try {
            int lastDotIndex = webViewName.lastIndexOf('.');
            if (lastDotIndex > 0 && lastDotIndex < webViewName.length() - 1) {
                String versionStr = webViewName.substring(lastDotIndex + 1);
                return Double.parseDouble(versionStr);
            }
        } catch (Exception e) {
            LOGGER.warning("iOS: Failed to parse version from: " + webViewName);
        }
        return -1; // Ako ne može da se parsira, vrati -1
    }
    /**
     * Waits for WebView to be ready using FluentWait pattern.
     * Uses consistent timeout handling with other wait methods.
     */
    private void waitForWebViewToBeReady() {
        LOGGER.info(Constants.WEBVIEW_WAITING_READY_MESSAGE);

        try {
            FluentWait<AppiumDriver> fluentWait = new FluentWait<>(driver)
                .withTimeout(Duration.ofSeconds(Constants.WEBVIEW_READINESS_TIMEOUT_SECONDS))
                .pollingEvery(Duration.ofSeconds(Constants.POLLING_INTERVAL_SECONDS))
                .ignoring(Exception.class);

            fluentWait.until(driver -> {
                try {
                    String pageSource = driver.getPageSource();
                    if (pageSource != null && pageSource.length() > 50) {
                        LOGGER.info(Constants.WEBVIEW_IS_READY_MESSAGE + pageSource.length());
                        return true;
                    }
                    return false;
                } catch (Exception e) {
                    return false;
                }
            });

        } catch (org.openqa.selenium.TimeoutException e) {
            LOGGER.warning(String.format(Constants.WEBVIEW_READINESS_TIMEOUT_MESSAGE, Constants.WEBVIEW_READINESS_TIMEOUT_SECONDS));
        }
    }

    private void waitForElementToBeVisible(WebElement element) {
        FluentWait<AppiumDriver> fluentWait = new FluentWait<>(driver).withTimeout(Duration.ofSeconds(20)).pollingEvery(Duration.ofSeconds(1)).ignoring(NoSuchElementException.class);

        fluentWait.until(ExpectedConditions.visibilityOf(element));
    }

    private void waitForElementToBeClickable(WebElement element) {
        FluentWait<AppiumDriver> fluentWait = new FluentWait<>(driver).withTimeout(Duration.ofSeconds(20)).pollingEvery(Duration.ofSeconds(1)).ignoring(NoSuchElementException.class);

        fluentWait.until(ExpectedConditions.elementToBeClickable(element));
    }

    private void swipe(int startX, int startY, int endX, int endY) {

        PointerInput finger = new PointerInput(PointerInput.Kind.TOUCH, "finger");
        Sequence swipe = new Sequence(finger, 1);

        swipe.addAction(finger.createPointerMove(Duration.ZERO, PointerInput.Origin.viewport(), startX, startY));
        swipe.addAction(finger.createPointerDown(PointerInput.MouseButton.LEFT.asArg()));
        swipe.addAction(finger.createPointerMove(Duration.ofMillis(500), PointerInput.Origin.viewport(), endX, endY));
        swipe.addAction(finger.createPointerUp(PointerInput.MouseButton.LEFT.asArg()));

        driver.perform(Collections.singletonList(swipe));
    }
}
