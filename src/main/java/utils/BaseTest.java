package utils;

import config.ConfigReader;
import constants.Localization;
import core.PlatformResolver;
import device.DeviceInfo;
import device.DevicePool;
import devicefarm.DeviceFarm;
import driver.DriverManager;
import io.appium.java_client.AppiumDriver;
import listener.TestListener;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestInfo;
import org.junit.jupiter.api.extension.ExtendWith;
import recording.Recording;

@ExtendWith(TestListener.class)
public abstract class BaseTest {
    public static final String DEFAULT_PROPERTY = "config.properties";
    protected static DeviceInfo device;
    private static boolean isDriverInitialized = false;

    @BeforeAll
    static void setUpClass() {
        ConfigReader config = ConfigReader.getInstance(DEFAULT_PROPERTY);
        Localization.loadLanguage(config.getProperty("language"));

        String platform = PlatformResolver.resolve();
        device = DevicePool.allocateDevice(platform);
        AppiumDriver driver = DriverManager.initializeDriver(device);
        isDriverInitialized = true;
    }

    @BeforeEach
    void setUp(TestInfo testInfo) {
        if (isDriverInitialized) {
            AppiumDriver driver = DriverManager.getDriver();
            DeviceFarm.setSessionName(driver, testInfo.getDisplayName());
            Recording.startVideo();
        }
    }

    @AfterEach
    void tearDown(TestInfo testInfo) {
        Recording.stopVideo(testInfo.getDisplayName());
    }

    @AfterAll
    static void tearDownClass() {
        if (isDriverInitialized) {
            DriverManager.quitDriver();
            DevicePool.releaseDevice(device);
            isDriverInitialized = false;
        }
    }
}
